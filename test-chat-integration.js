// Test script to verify chat integration
// Run this with: node test-chat-integration.js

const axios = require('axios');

const CHAT_API_BASE_URL = 'http://localhost:8084';
const CHAT_API_ENDPOINT = '/api/openai/chat';

async function testChatAPI() {
    console.log('🧪 Testing Bond AI Chat Integration...\n');
    
    try {
        console.log('📡 Testing connection to:', `${CHAT_API_BASE_URL}${CHAT_API_ENDPOINT}`);
        
        const testMessage = "Hello, can you help me?";
        console.log('💬 Sending test message:', testMessage);
        
        const response = await axios.post(
            `${CHAT_API_BASE_URL}${CHAT_API_ENDPOINT}`,
            testMessage,
            {
                headers: {
                    'Content-Type': 'text/plain',
                },
                timeout: 30000, // 30 seconds
            }
        );
        
        console.log('✅ Success! Response received:');
        console.log('📝 Status:', response.status);
        console.log('🤖 AI Response:', response.data);
        console.log('\n🎉 Chat integration is working correctly!');
        
    } catch (error) {
        console.log('❌ Error testing chat API:');
        
        if (error.code === 'ECONNREFUSED') {
            console.log('🔌 Connection refused - Backend server is not running');
            console.log('💡 Make sure to start the Spring Boot backend on port 8084');
        } else if (error.response) {
            console.log('📝 Status:', error.response.status);
            console.log('📄 Response:', error.response.data);
        } else {
            console.log('🐛 Error:', error.message);
        }
        
        console.log('\n🔧 Troubleshooting steps:');
        console.log('1. Ensure Spring Boot backend is running on port 8084');
        console.log('2. Check if Azure OpenAI credentials are configured correctly');
        console.log('3. Verify CORS settings allow requests from frontend');
    }
}

// Run the test
testChatAPI();

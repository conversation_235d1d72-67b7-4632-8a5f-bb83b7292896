import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import LandingPage1 from './components/LandingPage/LandingPage1';
import LandingPage2 from './components/LandingPage/LandingPage2';
import './App.css';
import ForgetPassword from './components/SignUpFlow/ForgetPassword';
import { useTranslation } from 'react-i18next';
import VendorRegistration from './components/SignUpFlow/VendorRF';
import SignInPage from './components/SignUpFlow/SignInPage';
import ForgetUsername from './components/SignUpFlow/ForgetUsername';
import Activate from './components/SignUpFlow/Activate';
import SetPassword from './components/SignUpFlow/SetPassword';
import Dashboard from './pages/Dashboard';
import Profile from './pages/Profile';
import MyOrders from './pages/MyOrders';
import NotificationsPage from './pages/NotificationsPage';
import PublicRoute from './components/PublicRoute';
import ProtectedRoute from './components/ProtectedRoute';
import FontDemo from './components/FontDemo/FontDemo';
import AdminPage from './components/LandingPage/AdminPage';
import RegistrationSuccess from "./components/SignUpFlow/RegistrationSuccess";
import UserEmail from "./components/ForgotPassword/UserEmail";
import User from './pages/User';
import Subscription from './components/Subscription/Subscription';
import CustomerManagement from './components/CustomerManagement/CustomerManagement';
import CreateOrder from  './components/CustomerManagement/createOrder';
import VendorOrder from './components/vendorManagement/vendor_order';
// import SimpleChatbotPage from './pages/SimpleChatbotPage';
// import FloatingChatDemo from './pages/FloatingChatDemo';
import FloatingChatWidget from './components/FloatingChatWidget/FloatingChatWidget';


const App = () => {
  const { i18n } = useTranslation();

  useEffect(() => {
    const savedLang = localStorage.getItem("i18nextLng") || "ar";
    const dir = savedLang === 'ar' ? 'rtl' : 'ltr';

    document.documentElement.lang = savedLang;
    document.documentElement.dir = dir;
    document.body.style.direction = dir;
    if (i18n.language !== savedLang) {
      i18n.changeLanguage(savedLang);
    }
  }, [i18n]);

  return (
    <>
      <Routes>
      {/* Public Routes */}
      <Route path="/" element={<LandingPage1 />} />
      <Route path="/page1" element={<LandingPage1 />} />
      <Route path="/page2" element={<LandingPage2 />} />
      <Route path="/plans" element={<Subscription />} />
       <Route path="/order" element={<CustomerManagement />} />
        <Route path="/createOrder" element={<CreateOrder />} />
        <Route path="/vendorOrder" element={<VendorOrder />} />
        {/* <Route path="/simple-chatbot" element={<SimpleChatbotPage />} /> */}
        {/* <Route path="/floating-chat-demo" element={<FloatingChatDemo />} /> */}





      {/* Auth Routes - Protected from authenticated users */}
      <Route element={<PublicRoute />}>
        <Route path="/signup" element={<VendorRegistration />} />
        <Route path="/signin" element={<SignInPage />} />
        <Route path="/activate" element={<Activate />} />
        <Route path="/set-password" element={<SetPassword />} />
        <Route path="/user-email" element={<UserEmail />} />
        <Route path="/forgot-password" element={<ForgetPassword />} />
        <Route path="/registration-success" element={<RegistrationSuccess />} />
      </Route>

      {/* Protected Routes - Requires authentication */}
      {/* <Route element={<ProtectedRoute />}> */}
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/profile" element={<Profile/>} />
        <Route path="/orders" element={<MyOrders />} />
        <Route path="/notifications" element={<NotificationsPage />} />
        <Route path="/admin" element={<AdminPage />} />
        <Route path="/manage-user" element={<User />} /> 
      {/* </Route> */}

      {/* 404 Route */}
      <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>

      {/* Floating Chat Widget - Available on all pages */}
      <FloatingChatWidget title="Bond AI" />
    </>
  );
};

export default App;


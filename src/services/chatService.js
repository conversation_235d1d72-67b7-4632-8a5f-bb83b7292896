// Chat API Service
import axios from 'axios';

// API Configuration
const CHAT_API_BASE_URL = import.meta.env.VITE_CHAT_API_BASE_URL || 'http://localhost:8084';
const CHAT_API_ENDPOINT = import.meta.env.VITE_CHAT_API_ENDPOINT || '/api/openai/chat';

// Create axios instance for chat API
const chatAPI = axios.create({
  baseURL: CHAT_API_BASE_URL,
  timeout: 30000, // 30 seconds timeout for AI responses
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
chatAPI.interceptors.request.use(
  (config) => {
    console.log('Chat API Request:', {
      url: config.url,
      method: config.method,
      data: config.data,
    });
    return config;
  },
  (error) => {
    console.error('Chat API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for logging and error handling
chatAPI.interceptors.response.use(
  (response) => {
    console.log('Chat API Response:', {
      status: response.status,
      data: response.data,
    });
    return response;
  },
  (error) => {
    console.error('Chat API Response Error:', {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
    });
    return Promise.reject(error);
  }
);

/**
 * Send a message to the AI chat service
 * @param {string} message - The user's message
 * @returns {Promise<string>} - The AI's response
 */
export const sendMessageToAI = async (message) => {
  try {
    if (!message || typeof message !== 'string' || message.trim().length === 0) {
      throw new Error('Message is required and must be a non-empty string');
    }

    const response = await chatAPI.post(CHAT_API_ENDPOINT, message.trim(), {
      headers: {
        'Content-Type': 'text/plain', // Backend expects plain text
      },
    });

    // Handle different response formats
    let aiResponse = response.data;
    
    // If response is an object, extract the message
    if (typeof aiResponse === 'object') {
      aiResponse = aiResponse.message || aiResponse.response || aiResponse.content || JSON.stringify(aiResponse);
    }
    
    // Ensure we return a string
    if (typeof aiResponse !== 'string') {
      aiResponse = String(aiResponse);
    }

    // Clean up the response (remove extra newlines, trim)
    aiResponse = aiResponse.trim();
    
    if (!aiResponse) {
      throw new Error('Empty response from AI service');
    }

    return aiResponse;
  } catch (error) {
    console.error('Error in sendMessageToAI:', error);
    
    // Handle different types of errors
    if (error.code === 'ECONNREFUSED') {
      throw new Error('Unable to connect to AI service. Please check if the backend is running.');
    } else if (error.response?.status === 404) {
      throw new Error('AI service endpoint not found. Please check the configuration.');
    } else if (error.response?.status === 500) {
      throw new Error('AI service encountered an internal error. Please try again later.');
    } else if (error.response?.status === 429) {
      throw new Error('Too many requests. Please wait a moment before trying again.');
    } else if (error.code === 'ECONNABORTED') {
      throw new Error('Request timeout. The AI service is taking too long to respond.');
    } else if (error.message.includes('Network Error')) {
      throw new Error('Network error. Please check your internet connection.');
    } else {
      // Re-throw the original error message if it's already user-friendly
      throw new Error(error.message || 'Failed to get response from AI service');
    }
  }
};

/**
 * Check if the chat service is available
 * @returns {Promise<boolean>} - True if service is available
 */
export const checkChatServiceHealth = async () => {
  try {
    // Try to send a simple health check message
    await sendMessageToAI('Hello');
    return true;
  } catch (error) {
    console.warn('Chat service health check failed:', error.message);
    return false;
  }
};

/**
 * Get chat service configuration
 * @returns {object} - Configuration object
 */
export const getChatServiceConfig = () => {
  return {
    baseURL: CHAT_API_BASE_URL,
    endpoint: CHAT_API_ENDPOINT,
    fullURL: `${CHAT_API_BASE_URL}${CHAT_API_ENDPOINT}`,
  };
};

export default {
  sendMessageToAI,
  checkChatServiceHealth,
  getChatServiceConfig,
};

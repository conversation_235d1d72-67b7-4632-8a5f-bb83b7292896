package com.aichat.ai_chat;

import com.aichat.ai_chat.AzureOpenAIService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/openai")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:5173"}, allowCredentials = "true")
public class AzureOpenAIController {

    @Autowired
    private AzureOpenAIService azureOpenAIService;
    @PostMapping("/chat")
    public String chat(@RequestBody String userMessage) {
        return azureOpenAIService.getChatResponse(userMessage);
    }
}
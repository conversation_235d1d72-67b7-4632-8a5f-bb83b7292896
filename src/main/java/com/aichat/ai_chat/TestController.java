package com.aichat.ai_chat;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:5173"}, allowCredentials = "true")
public class TestController {

    @Autowired
    private TrainingDataService trainingDataService;

    /**
     * Test endpoint to verify training data loading
     */
    @GetMapping("/training-data")
    public String testTrainingData() {
        try {
            String context = trainingDataService.getRelevantContext("cloud services");
            return "Training Data Test:\n\n" + context;
        } catch (Exception e) {
            return "Error loading training data: " + e.getMessage();
        }
    }

    /**
     * Test endpoint for platform statistics
     */
    @GetMapping("/platform-stats")
    public String testPlatformStats() {
        try {
            return trainingDataService.getPlatformStats();
        } catch (Exception e) {
            return "Error getting platform stats: " + e.getMessage();
        }
    }

    /**
     * Test endpoint for specific query context
     */
    @GetMapping("/context/{query}")
    public String testContext(@PathVariable String query) {
        try {
            return "Context for query: " + query + "\n\n" + 
                   trainingDataService.getRelevantContext(query);
        } catch (Exception e) {
            return "Error getting context: " + e.getMessage();
        }
    }
}

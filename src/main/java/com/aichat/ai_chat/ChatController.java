package com.aichat.ai_chat;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/chat")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:5173"}, allowCredentials = "true")
public class ChatController {

    @Autowired
    private AzureOpenAIService azureOpenAIService;
    
    @Autowired
    private TrainingDataService trainingDataService;

    /**
     * Enhanced chat endpoint with better response structure
     */
    @PostMapping("/message")
    public ResponseEntity<Map<String, Object>> sendMessage(@RequestBody Map<String, String> request) {
        try {
            String userMessage = request.get("message");
            if (userMessage == null || userMessage.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(createErrorResponse("Message cannot be empty"));
            }

            String aiResponse = azureOpenAIService.getChatResponse(userMessage.trim());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", aiResponse.trim());
            response.put("timestamp", System.currentTimeMillis());
            response.put("model", "Bond AI (Azure OpenAI GPT-4o-mini)");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            System.err.println("Error processing chat message: " + e.getMessage());
            return ResponseEntity.internalServerError()
                .body(createErrorResponse("Failed to process your message. Please try again."));
        }
    }

    /**
     * Get platform statistics and information
     */
    @GetMapping("/platform-info")
    public ResponseEntity<Map<String, Object>> getPlatformInfo() {
        try {
            String stats = trainingDataService.getPlatformStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("platformStats", stats);
            response.put("description", "B2B marketplace connecting companies with IT service vendors");
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(createErrorResponse("Failed to retrieve platform information"));
        }
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("status", "healthy");
        response.put("service", "Bond AI Chat Service");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }

    /**
     * Get relevant context for a query (for debugging)
     */
    @PostMapping("/context")
    public ResponseEntity<Map<String, Object>> getContext(@RequestBody Map<String, String> request) {
        try {
            String query = request.get("query");
            if (query == null || query.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(createErrorResponse("Query cannot be empty"));
            }

            String context = trainingDataService.getRelevantContext(query.trim());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("query", query.trim());
            response.put("context", context);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(createErrorResponse("Failed to retrieve context"));
        }
    }

    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> error = new HashMap<>();
        error.put("success", false);
        error.put("error", message);
        error.put("timestamp", System.currentTimeMillis());
        return error;
    }
}

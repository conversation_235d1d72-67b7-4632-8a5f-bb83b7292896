package com.aichat.ai_chat;

import com.azure.ai.openai.OpenAIClient;
import com.azure.ai.openai.OpenAIClientBuilder;
import com.azure.ai.openai.models.ChatChoice;
import com.azure.ai.openai.models.ChatCompletions;
import com.azure.ai.openai.models.ChatCompletionsOptions;
import com.azure.ai.openai.models.ChatRequestMessage;
import com.azure.ai.openai.models.ChatRequestSystemMessage;
import com.azure.ai.openai.models.ChatRequestUserMessage;
import com.azure.ai.openai.models.ChatResponseMessage;
import com.azure.core.credential.AzureKeyCredential;


import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
public class AzureOpenAIService {

    @Value("${azure.openai.api-key}")
    private String apiKey;

    @Value("${azure.openai.endpoint}")
    private String endpoint;

    @Value("${azure.openai.deployment-name}")
    private String deploymentName;

    private OpenAIClient client;    
    
    @PostConstruct
    public void initializeClient() {
        System.out.println("Initializing OpenAI client with endpoint: " + endpoint);
        System.out.println("Deployment name: " + deploymentName);
        try {
            client = new OpenAIClientBuilder()
                    .credential(new AzureKeyCredential(apiKey))
                    .endpoint(endpoint)
                    .buildClient();
            System.out.println("OpenAI client initialized successfully");
        } catch (Exception e) {
            System.err.println("Failed to initialize OpenAI client: " + e.getMessage());
            throw new RuntimeException("Failed to initialize OpenAI client", e);
        }
    }

    public String getChatResponse(String userMessage) {
        System.out.println("Preparing chat completion request for deployment: " + deploymentName);
        List<ChatRequestMessage> chatMessages = Arrays.asList(
                new ChatRequestSystemMessage("You are Bond AI, a helpful and intelligent assistant for a B2B platform. You help users with business-related questions, provide professional advice, and assist with platform navigation. Be concise, professional, and friendly in your responses."),
                new ChatRequestUserMessage(userMessage)
        );

        ChatCompletionsOptions options = new ChatCompletionsOptions(chatMessages);
        options.setMaxTokens(4096);
        options.setTemperature(1d);
        options.setTopP(1d);

        ChatCompletions completions = client.getChatCompletions(deploymentName, options);

        StringBuilder response = new StringBuilder();
        for (ChatChoice choice : completions.getChoices()) {
            ChatResponseMessage message = choice.getMessage();
            response.append(message.getContent()).append("\n");
        }
        return response.toString();
    }
}
package com.aichat.ai_chat;

import com.azure.ai.openai.OpenAIClient;
import com.azure.ai.openai.OpenAIClientBuilder;
import com.azure.ai.openai.models.ChatChoice;
import com.azure.ai.openai.models.ChatCompletions;
import com.azure.ai.openai.models.ChatCompletionsOptions;
import com.azure.ai.openai.models.ChatRequestMessage;
import com.azure.ai.openai.models.ChatRequestSystemMessage;
import com.azure.ai.openai.models.ChatRequestUserMessage;
import com.azure.ai.openai.models.ChatResponseMessage;
import com.azure.core.credential.AzureKeyCredential;


import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
public class AzureOpenAIService {

    @Value("${azure.openai.api-key}")
    private String apiKey;

    @Value("${azure.openai.endpoint}")
    private String endpoint;

    @Value("${azure.openai.deployment-name}")
    private String deploymentName;

    private OpenAIClient client;

    @Autowired
    private TrainingDataService trainingDataService;
    
    @PostConstruct
    public void initializeClient() {
        System.out.println("Initializing OpenAI client with endpoint: " + endpoint);
        System.out.println("Deployment name: " + deploymentName);
        try {
            client = new OpenAIClientBuilder()
                    .credential(new AzureKeyCredential(apiKey))
                    .endpoint(endpoint)
                    .buildClient();
            System.out.println("OpenAI client initialized successfully");
        } catch (Exception e) {
            System.err.println("Failed to initialize OpenAI client: " + e.getMessage());
            throw new RuntimeException("Failed to initialize OpenAI client", e);
        }
    }

    public String getChatResponse(String userMessage) {
        System.out.println("Preparing chat completion request for deployment: " + deploymentName);

        // Get relevant context from training data
        String relevantContext = trainingDataService.getRelevantContext(userMessage);

        // Enhanced system message with domain knowledge
        String systemMessage = buildSystemMessage(relevantContext);

        List<ChatRequestMessage> chatMessages = Arrays.asList(
                new ChatRequestSystemMessage(systemMessage),
                new ChatRequestUserMessage(userMessage)
        );

        ChatCompletionsOptions options = new ChatCompletionsOptions(chatMessages);
        options.setMaxTokens(4096);
        options.setTemperature(1d);
        options.setTopP(1d);

        ChatCompletions completions = client.getChatCompletions(deploymentName, options);

        StringBuilder response = new StringBuilder();
        for (ChatChoice choice : completions.getChoices()) {
            ChatResponseMessage message = choice.getMessage();
            response.append(message.getContent()).append("\n");
        }
        return response.toString();
    }

    private String buildSystemMessage(String relevantContext) {
        StringBuilder systemMessage = new StringBuilder();

        systemMessage.append("You are Bond AI, an intelligent assistant for a B2B marketplace platform that connects companies with IT service vendors.\n\n");

        systemMessage.append("YOUR ROLE:\n");
        systemMessage.append("- Help companies find suitable IT services and vendors\n");
        systemMessage.append("- Assist vendors in understanding market opportunities\n");
        systemMessage.append("- Provide guidance on platform features and navigation\n");
        systemMessage.append("- Facilitate connections between buyers and service providers\n\n");

        systemMessage.append("COMMUNICATION STYLE:\n");
        systemMessage.append("- Professional and business-focused\n");
        systemMessage.append("- Concise yet informative\n");
        systemMessage.append("- Helpful and solution-oriented\n");
        systemMessage.append("- Use business terminology appropriately\n\n");

        systemMessage.append("PLATFORM CONTEXT:\n");
        systemMessage.append(relevantContext);
        systemMessage.append("\n");

        systemMessage.append("INSTRUCTIONS:\n");
        systemMessage.append("- Use the provided context to give relevant, accurate responses\n");
        systemMessage.append("- When discussing services, reference similar examples from the platform\n");
        systemMessage.append("- Provide budget ranges and timeline estimates when relevant\n");
        systemMessage.append("- Suggest appropriate service categories and vendors when applicable\n");
        systemMessage.append("- If you don't have specific information, be honest but offer to help find it\n");

        return systemMessage.toString();
    }
}
package com.aichat.ai_chat;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TrainingDataService {

    private List<Map<String, Object>> customerOrders;
    private List<Map<String, Object>> vendorProposals;
    private ObjectMapper objectMapper;

    @PostConstruct
    public void loadTrainingData() {
        objectMapper = new ObjectMapper();
        try {
            loadCustomerOrders();
            loadVendorProposals();
            System.out.println("Training data loaded successfully:");
            System.out.println("- Customer Orders: " + customerOrders.size());
            System.out.println("- Vendor Proposals: " + vendorProposals.size());
        } catch (IOException e) {
            System.err.println("Failed to load training data: " + e.getMessage());
            throw new RuntimeException("Failed to load training data", e);
        }
    }

    private void loadCustomerOrders() throws IOException {
        ClassPathResource resource = new ClassPathResource("training-data/customerOrders.json");
        customerOrders = objectMapper.readValue(
            resource.getInputStream(), 
            new TypeReference<List<Map<String, Object>>>() {}
        );
    }

    private void loadVendorProposals() throws IOException {
        ClassPathResource resource = new ClassPathResource("training-data/vendorproposals.json");
        vendorProposals = objectMapper.readValue(
            resource.getInputStream(), 
            new TypeReference<List<Map<String, Object>>>() {}
        );
    }

    /**
     * Get relevant context based on user query
     */
    public String getRelevantContext(String userQuery) {
        StringBuilder context = new StringBuilder();
        
        // Add platform overview
        context.append("PLATFORM OVERVIEW:\n");
        context.append("This is a B2B marketplace platform connecting companies with IT service vendors.\n\n");
        
        // Add service categories
        context.append("AVAILABLE SERVICE CATEGORIES:\n");
        getServiceCategories().forEach(category -> 
            context.append("- ").append(category).append("\n"));
        context.append("\n");
        
        // Add industry focus
        context.append("SUPPORTED INDUSTRIES:\n");
        getIndustries().forEach(industry -> 
            context.append("- ").append(industry).append("\n"));
        context.append("\n");
        
        // Add relevant examples based on query
        if (containsKeywords(userQuery, "order", "project", "requirement", "need")) {
            context.append("EXAMPLE CUSTOMER ORDERS:\n");
            getRelevantOrders(userQuery, 3).forEach(order -> {
                context.append("• ").append(order.get("title")).append("\n");
                context.append("  Description: ").append(order.get("description")).append("\n");
                context.append("  Industry: ").append(order.get("industry")).append("\n");
                context.append("  Budget: ").append(order.get("budget")).append(" ").append(order.get("currency")).append("\n\n");
            });
        }
        
        if (containsKeywords(userQuery, "vendor", "proposal", "service", "provider")) {
            context.append("EXAMPLE VENDOR CAPABILITIES:\n");
            getRelevantProposals(userQuery, 3).forEach(proposal -> {
                context.append("• ").append(proposal.get("proposal_text")).append("\n");
                context.append("  Quote: ").append(proposal.get("quoted_amount")).append(" USD\n");
                context.append("  Duration: ").append(proposal.get("duration_days")).append(" days\n\n");
            });
        }
        
        return context.toString();
    }

    private boolean containsKeywords(String text, String... keywords) {
        String lowerText = text.toLowerCase();
        for (String keyword : keywords) {
            if (lowerText.contains(keyword.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    private List<String> getServiceCategories() {
        return customerOrders.stream()
            .map(order -> (String) order.get("order_type"))
            .distinct()
            .map(this::formatServiceType)
            .collect(Collectors.toList());
    }

    private List<String> getIndustries() {
        return customerOrders.stream()
            .map(order -> (String) order.get("industry"))
            .distinct()
            .collect(Collectors.toList());
    }

    private List<Map<String, Object>> getRelevantOrders(String query, int limit) {
        // Simple relevance matching - can be enhanced with more sophisticated algorithms
        return customerOrders.stream()
            .filter(order -> isRelevantOrder(order, query))
            .limit(limit)
            .collect(Collectors.toList());
    }

    private List<Map<String, Object>> getRelevantProposals(String query, int limit) {
        return vendorProposals.stream()
            .filter(proposal -> isRelevantProposal(proposal, query))
            .limit(limit)
            .collect(Collectors.toList());
    }

    private boolean isRelevantOrder(Map<String, Object> order, String query) {
        String queryLower = query.toLowerCase();
        String title = ((String) order.get("title")).toLowerCase();
        String description = ((String) order.get("description")).toLowerCase();
        String industry = ((String) order.get("industry")).toLowerCase();
        String orderType = ((String) order.get("order_type")).toLowerCase();
        
        return title.contains(queryLower) || 
               description.contains(queryLower) || 
               industry.contains(queryLower) ||
               orderType.contains(queryLower) ||
               containsRelatedTerms(queryLower, title + " " + description);
    }

    private boolean isRelevantProposal(Map<String, Object> proposal, String query) {
        String queryLower = query.toLowerCase();
        String proposalText = ((String) proposal.get("proposal_text")).toLowerCase();
        
        return proposalText.contains(queryLower) ||
               containsRelatedTerms(queryLower, proposalText);
    }

    private boolean containsRelatedTerms(String query, String content) {
        // Add domain-specific term matching
        if (query.contains("cloud") && content.contains("azure")) return true;
        if (query.contains("app") && content.contains("mobile")) return true;
        if (query.contains("security") && content.contains("audit")) return true;
        if (query.contains("data") && content.contains("analytics")) return true;
        if (query.contains("erp") && content.contains("enterprise")) return true;
        
        return false;
    }

    private String formatServiceType(String orderType) {
        String[] words = orderType.replace("_", " ").toLowerCase().split(" ");
        StringBuilder formatted = new StringBuilder();
        for (String word : words) {
            formatted.append(Character.toUpperCase(word.charAt(0)))
                     .append(word.substring(1))
                     .append(" ");
        }
        return formatted.toString().trim();
    }

    /**
     * Get platform statistics for informational responses
     */
    public String getPlatformStats() {
        int totalOrders = customerOrders.size();
        int totalProposals = vendorProposals.size();
        long avgBudget = customerOrders.stream()
            .mapToLong(order -> ((Number) order.get("budget")).longValue())
            .sum() / totalOrders;
        
        return String.format(
            "Platform Statistics:\n" +
            "- Total Active Orders: %d\n" +
            "- Total Vendor Proposals: %d\n" +
            "- Average Project Budget: $%,d USD\n" +
            "- Service Categories: %d\n" +
            "- Industries Served: %d",
            totalOrders, totalProposals, avgBudget, 
            getServiceCategories().size(), getIndustries().size()
        );
    }
}

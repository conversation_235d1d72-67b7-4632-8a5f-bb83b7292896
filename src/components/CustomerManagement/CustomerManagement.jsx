import React, { useState } from 'react';
import { Table, Input, Button, Space, Rate } from 'antd';
import { SearchOutlined, ExportOutlined, PlusOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { customerData } from './customerData';
import './CustomerManagement.css';
import Header from '../Header-Section/Header';
import { useNavigate } from 'react-router-dom';


const CustomerManagement = () => {
  const { t } = useTranslation();
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [searchText, setSearchText] = useState('');
  // Add this after existing useState declarations
const [selectedService, setSelectedService] = useState(null);
  const navigate = useNavigate();

  const columns = [
    {
      title: t('Service'),
      dataIndex: 'service',
      key: 'service',
      filteredValue: [searchText],
      onFilter: (value, record) => {
        return String(t(record.service))
          .toLowerCase()
          .includes(value.toLowerCase());
      },
      render: (text) => t(text),
    },
    {
      title: t('Price'),
      dataIndex: 'price',
      key: 'price',
    },
    {
      title: t('Ratings'),
      dataIndex: 'ratings',
      key: 'ratings',
      render: (rating) => <Rate disabled defaultValue={rating} />,
    },
    {
      title: t('Category'),
      dataIndex: 'category',
      key: 'category',
      render: (text) => t(text),
    },
    {
      title: t('Vendor'),
      dataIndex: 'vendor',
      key: 'vendor',
      render: (text) => t(text),
    },
  ];

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const handleSearch = (e) => {
    setSearchText(e.target.value);
  };

  return (
    <>
      <Header />
      <div className="customer-management">
        {/* Search Bar */}
        <div className="search-bar">
          <Input
            placeholder={t('Search for a product or service...')}
            prefix={<SearchOutlined />}
            onChange={handleSearch}
            className="search-input-large"
          />
        </div>

        {/* Header Section */}
        <div className="product-list-header">
          <h2 className="product-list-title">{t('Product List')}</h2>
          <Space className="header-buttons">
            <Button icon={<ExportOutlined />} className="export-button">
              {t('Export')}
            </Button>
            <Button className="compare-button">{t('Compare')}</Button>
           <Button 
                  icon={<PlusOutlined />} 
                  className="create-button" 
               onClick={() => {
              if (selectedRowKeys.length === 0) {
                alert('Please select a service first');
                return;
              }
              const selectedServices = customerData.filter(item => selectedRowKeys.includes(item.id));
              navigate('/createOrder', { state: { selectedServices: selectedServices } });
            }} >
                {t('Create Order')}
            </Button>
          </Space>
        </div>

        {/* Table Section */}
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={customerData}
          className="customer-table"
          rowKey="id"
        />
      </div>
    </>
  );
};

export default CustomerManagement;
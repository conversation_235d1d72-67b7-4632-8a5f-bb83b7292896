import React, { useState, useEffect, useRef } from 'react';
import { 
  Card, 
  Input, 
  Button, 
  Typography, 
  Space, 
  Spin, 
  message as antMessage
} from 'antd';
import { 
  SendOutlined, 
  ClearOutlined,
  CloseOutlined,
  LoadingOutlined 
} from '@ant-design/icons';
import logoImage from '../../assets/logo.png';
import './ChatBot.css';
import { sendMessageToAI } from '../../services/chatService';

const { TextArea } = Input;
const { Text } = Typography;

// Chat Header Component
const ChatHeader = ({ onClear, onMinimize, title = "Bond AI" }) => (
  <div className="chat-header">
    <Space>
      {/* <div className="chat-logo-container">
        <img 
          src={logoImage} 
          alt="Bond AI Logo" 
          className="chat-logo"
        />
      </div> */}
      <div>
        <Text strong style={{ color: 'white',textAlign:'left' ,marginLeft:'-30px'}}>{title}</Text>
        <br />
        <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '12px' }}>
          Online • Ready to help
        </Text>
      </div>
    </Space>
    <Space>
      <Button 
        type="text" 
        icon={<ClearOutlined />} 
        onClick={onClear}
        style={{ color: 'white' }}
        title="Clear conversation"
        size="small"
      />
      {onMinimize && (
        <Button 
          type="text" 
          icon={<CloseOutlined />} 
          onClick={onMinimize}
          style={{ color: 'white' }}
          title="Close chat"
          size="small"
        />
      )}
    </Space>
  </div>
);

// Message Component
const Message = ({ message, isUser, timestamp }) => (
  <div className={`message ${isUser ? 'user-message' : 'ai-message'}`}>
    <div className="message-content">
      <div className="message-bubble">
        <Text style={{color: isUser ? 'white' : 'black'}}>{message}</Text>
      </div>
      <Text className="message-timestamp" type="secondary">
        {new Date(timestamp).toLocaleTimeString([], { 
          hour: '2-digit', 
          minute: '2-digit' 
        })}
      </Text>
    </div>
  </div>
);

// Typing Indicator Component
const TypingIndicator = ({ show }) => {
  if (!show) return null;
  
  return (
    <div className="message ai-message">
      <div className="message-content">
        <div className="message-bubble typing-indicator">
          <Space>
            <Spin indicator={<LoadingOutlined style={{ fontSize: 14 }} spin />} />
            <Text type="secondary">Bond AI is typing...</Text>
          </Space>
        </div>
      </div>
    </div>
  );
};

// Chat Body Component
const ChatBody = ({ messages, isTyping }) => {
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  return (
    <div className="chat-body">
      {messages.length === 0 && (
        <div className="welcome-message">
          <div className="welcome-logo-container">
            <img 
              src={logoImage} 
              alt="Bond AI Logo" 
              className="welcome-logo"
            />
          </div>
          <Text type="secondary" className="welcome-text">
            Hello! I'm Bond AI, your intelligent assistant. How can I help you today?
          </Text>
        </div>
      )}
      
      {messages.map((msg) => (
        <Message
          key={msg.id}
          message={msg.text}
          isUser={msg.isUser}
          timestamp={msg.timestamp}
        />
      ))}
      
      <TypingIndicator show={isTyping} />
      <div ref={messagesEndRef} />
    </div>
  );
};

// Modern Chat Input Component
const ChatInput = ({ onSendMessage, disabled }) => {
  const [inputValue, setInputValue] = useState('');

  const handleSend = () => {
    if (inputValue.trim() && !disabled) {
      onSendMessage(inputValue.trim());
      setInputValue('');
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="modern-chat-input">
      <div className="input-container">
        <TextArea
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Type your message..."
          autoSize={{ minRows: 1, maxRows: 4 }}
          disabled={disabled}
          className="modern-textarea"
          variant="borderless"
        />
        <Button
          type="primary"
          shape="circle"
          icon={<SendOutlined />}
          onClick={handleSend}
          disabled={!inputValue.trim() || disabled}
          loading={disabled}
          className="modern-send-button"
          size="large"
        />
      </div>
    </div>
  );
};

// Main ChatBot Component
const ChatBot = ({ 
  style = {}, 
  className = '',
  title = "Bond AI",
  height = 600,
  width = '100%',
  onMinimize
}) => {
  const [messages, setMessages] = useState([]);
  const [isTyping, setIsTyping] = useState(false);

  const handleSendMessage = async (messageText) => {
    // Add user message
    const userMessage = {
      id: Date.now(),
      text: messageText,
      isUser: true,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    try {
      // Call AI API
      const aiResponse = await sendMessageToAI(messageText);
      
      // Add AI response
      const aiMessage = {
        id: Date.now() + 1,
        text: aiResponse,
        isUser: false,
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error sending message:', error);

      // Add error message to chat
      const errorMessage = {
        id: Date.now() + 2,
        text: `Sorry, I encountered an error: ${error.message}`,
        isUser: false,
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);

      // Show notification
      antMessage.error(error.message || 'Failed to send message. Please try again.');
    } finally {
      setIsTyping(false);
    }
  };

  const handleClearChat = () => {
    setMessages([]);
    antMessage.success('Conversation cleared');
  };

  return (
    <Card
      className={`chatbot-container ${className}`}
      style={{ 
        height, 
        width, 
        display: 'flex', 
        flexDirection: 'column',
        ...style 
      }}
      styles={{ 
        body: {
          padding: 0, 
          height: '100%', 
          display: 'flex', 
          flexDirection: 'column' 
        }
      }}
    >
      <ChatHeader onClear={handleClearChat} onMinimize={onMinimize} title={title} />
      <ChatBody messages={messages} isTyping={isTyping} />
      <ChatInput 
        onSendMessage={handleSendMessage} 
        disabled={isTyping} 
      />
    </Card>
  );
};

export default ChatBot;
